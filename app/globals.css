@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* React-PDF TextLayer styles */
.react-pdf__Page__textContent {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  opacity: 0.2;
  line-height: 1;
}

.react-pdf__Page__textContent span {
  position: absolute;
  white-space: pre;
  cursor: text;
  transform-origin: 0% 0%;
}

/* Make text selectable in PDF viewer */
.react-pdf__Page__textContent span:hover {
  background-color: rgba(0, 123, 255, 0.1);
}

.react-pdf__Page__textContent span::selection {
  background-color: rgba(0, 123, 255, 0.3);
}
