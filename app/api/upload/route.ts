import { NextRequest, NextResponse } from 'next/server'
import { put } from '@vercel/blob'
import { auth } from '@/auth'
import { getUserFilePath } from '@/app/lib/blob-storage'
import { FILE_VALIDATION } from '@/app/lib/file-validation'

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get the form data
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    // Validate file type - only allow PDFs
    const fileType = file.type
    const fileExtension = file.name.split('.').pop()?.toLowerCase()

    if (!FILE_VALIDATION.ALLOWED_TYPES.includes(fileType) &&
        !FILE_VALIDATION.ALLOWED_EXTENSIONS.includes(fileExtension || '')) {
      return NextResponse.json(
        { error: FILE_VALIDATION.MESSAGES.INVALID_TYPE },
        { status: 400 }
      )
    }

    // Validate file size
    if (file.size > FILE_VALIDATION.MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: FILE_VALIDATION.MESSAGES.FILE_TOO_LARGE },
        { status: 400 }
      )
    }

    // Create user-specific file path
    const userFilePath = await getUserFilePath(file.name)

    console.log(`Uploading file: ${file.name} (${file.size} bytes) to path: ${userFilePath}`)

    // Upload to Vercel Blob
    const blob = await put(userFilePath, file, {
      access: 'public',
    })

    console.log(`Upload successful:`, {
      url: blob.url,
      pathname: blob.pathname,
      size: blob.size,
      uploadedAt: blob.uploadedAt
    })

    // Return the blob data with success confirmation
    return NextResponse.json({
      ...blob,
      success: true,
      message: 'File uploaded successfully'
    })
  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json(
      { error: 'Upload failed' },
      { status: 500 }
    )
  }
}

// Required for larger file uploads
export const config = {
  api: {
    bodyParser: false,
  },
}