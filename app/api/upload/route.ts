import { NextRequest, NextResponse } from 'next/server'
import { put } from '@vercel/blob'
import { auth } from '@/auth'
import { getUserFilePath } from '@/app/lib/blob-storage'

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get the form data
    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    // Create user-specific file path
    const userFilePath = await getUserFilePath(file.name)
    
    // Upload to Vercel Blob
    const blob = await put(userFilePath, file, {
      access: 'public',
    })

    // Return the blob data
    return NextResponse.json(blob)
  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json(
      { error: 'Upload failed' },
      { status: 500 }
    )
  }
}

// Required for larger file uploads
export const config = {
  api: {
    bodyParser: false,
  },
}