import { NextRequest, NextResponse } from 'next/server'
import { list, head } from '@vercel/blob'
import { auth } from '@/auth'

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const filename = searchParams.get('filename')

    if (!filename) {
      return NextResponse.json(
        { error: 'Filename parameter required' },
        { status: 400 }
      )
    }

    // List all files for this user
    const userPrefix = `users/${session.user.id}/`
    console.log(`Verifying upload for user ${session.user.id}, filename: ${filename}`)

    const { blobs } = await list({ prefix: userPrefix })
    console.log(`Found ${blobs.length} total files for user`)

    // Look for the specific file
    const matchingFile = blobs.find(blob => {
      const blobFilename = blob.pathname.replace(userPrefix, '')
      return blobFilename === filename || blobFilename.startsWith(filename.split('.')[0])
    })

    if (matchingFile) {
      // Verify the file exists and get its metadata
      try {
        const fileInfo = await head(matchingFile.url)
        console.log(`File verified:`, {
          pathname: matchingFile.pathname,
          url: matchingFile.url,
          size: matchingFile.size,
          uploadedAt: matchingFile.uploadedAt,
          contentType: fileInfo.contentType
        })

        return NextResponse.json({
          success: true,
          file: {
            name: matchingFile.pathname.replace(userPrefix, ''),
            url: matchingFile.url,
            size: matchingFile.size,
            uploadedAt: matchingFile.uploadedAt,
            contentType: fileInfo.contentType
          },
          message: 'File successfully uploaded and verified in blob storage'
        })
      } catch (headError) {
        console.error('Error getting file metadata:', headError)
        return NextResponse.json({
          success: false,
          error: 'File found in listing but metadata check failed',
          file: {
            name: matchingFile.pathname.replace(userPrefix, ''),
            url: matchingFile.url,
            size: matchingFile.size,
            uploadedAt: matchingFile.uploadedAt
          }
        })
      }
    } else {
      console.log(`File not found. Available files:`)
      blobs.forEach((blob, index) => {
        console.log(`  ${index + 1}: ${blob.pathname.replace(userPrefix, '')}`)
      })

      return NextResponse.json({
        success: false,
        error: 'File not found in blob storage',
        availableFiles: blobs.map(blob => blob.pathname.replace(userPrefix, ''))
      })
    }

  } catch (error) {
    console.error('Verification error:', error)
    return NextResponse.json(
      { error: 'Verification failed', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    )
  }
}
