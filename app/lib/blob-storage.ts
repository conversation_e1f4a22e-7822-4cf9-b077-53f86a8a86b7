
'use server'

import { put, list, del, head } from '@vercel/blob';
import { auth } from '@/auth';

// Test function to verify your setup
export async function testBlobStorage() {
  try {
    // Create a test blob
    const testBlob = await put('test-file.txt', 'Hello, Vercel Blob!', {
      access: 'public',
    });
    
    console.log('Test blob created:', testBlob);
    
    // List blobs to verify
    const blobs = await list();
    console.log('Blobs in store:', blobs);
    
    // Clean up test blob
    // await del(testBlob.url);
    console.log('Test blob deleted');
    
    return { success: true, message: 'Blob storage is working correctly!' };
  } catch (error) {
    console.error('Blob storage test failed:', error);
    return { 
      success: false, 
      message: 'Blob storage test failed', 
      error: error instanceof Error ? error.message : String(error) 
    };
  }
}

// Ensure user can only access their own files
export async function getUserFilePath(path: string = '') {
  const session = await auth();
  
  // Add a more detailed error message
  if (!session?.user?.id) {
    console.error('Authentication failed: No user session found');
    throw new Error('User not authenticated');
  }
  
  // Normalize path to remove leading/trailing slashes
  const normalizedPath = path.replace(/^\/+|\/+$/g, '');
  
  // Create user-specific path prefix
  return `users/${session.user.id}/${normalizedPath}`;
}

// Get user's files
export async function getUserFiles() {
  try {
    const session = await auth();
    
    // Add more detailed logging for debugging
    console.log("Auth session in getUserFiles:", JSON.stringify({
      exists: !!session,
      hasUser: !!session?.user,
      hasUserId: !!session?.user?.id,
      userId: session?.user?.id
    }));
    
    if (!session?.user?.id) {
      console.error('Authentication failed: No user ID in session');
      throw new Error('User not authenticated');
    }
    
    // List files with the user's prefix
    const userPrefix = `users/${session.user.id}/`;
    console.log("Listing files with prefix:", userPrefix);
    
    const { blobs } = await list({ prefix: userPrefix });
    console.log("Files found:", blobs.length);
    
    // Transform the data for the UI
    return blobs.map(blob => ({
      id: blob.pathname,
      name: blob.pathname.replace(userPrefix, ''),
      url: blob.url,
      size: blob.size,
      uploadedAt: blob.uploadedAt
    }));
  } catch (error) {
    console.error('Error in getUserFiles:', error);
    throw error;
  }
}

// Delete a user file
export async function deleteUserFile(pathname: string) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      console.error('Authentication failed in deleteUserFile: No user ID in session');
      throw new Error('User not authenticated');
    }
    
    console.log(`Original pathname received: ${pathname}`);
    
    // Ensure the file belongs to the user
    const userPrefix = `users/${session.user.id}/`;
    if (!pathname.startsWith(userPrefix)) {
      console.error(`Unauthorized access attempt: ${pathname} does not start with ${userPrefix}`);
      throw new Error('Unauthorized access to file');
    }
    
    // Extract the filename without the user prefix
    const filenameWithoutPrefix = pathname.replace(userPrefix, '');
    console.log(`Filename without prefix: ${filenameWithoutPrefix}`);
    
    // Get all files for this user
    console.log(`Listing all files with prefix: ${userPrefix}`);
    const { blobs } = await list({ prefix: userPrefix });
    console.log(`Found ${blobs.length} files for user`);
    
    // Log all found files for debugging
    blobs.forEach((blob, index) => {
      console.log(`File ${index + 1}: ${blob.pathname}`);
    });
    
    // Find the exact file or one with a matching base name plus Vercel's suffix
    const matchingFile = blobs.find(blob => {
      // Get filename without the user prefix
      const blobFilename = blob.pathname.replace(userPrefix, '');
      
      // Check for exact match first
      if (blobFilename === filenameWithoutPrefix) {
        console.log(`Found exact match: ${blobFilename}`);
        return true;
      }
      
      // Extract the base name (before any potential suffix)
      // Vercel typically adds suffixes with a dash, like: filename-suffix.ext
      const baseNameWithExt = filenameWithoutPrefix.split('-')[0];
      const fileExt = filenameWithoutPrefix.includes('.') ? 
        filenameWithoutPrefix.split('.').pop() : '';
      
      // Check if this blob's filename starts with our base name and has the same extension
      const blobBaseNameWithExt = blobFilename.split('-')[0];
      const blobExt = blobFilename.includes('.') ? 
        blobFilename.split('.').pop() : '';
      
      const isBaseNameMatch = blobBaseNameWithExt === baseNameWithExt && blobExt === fileExt;
      
      console.log(`Checking if ${blobFilename} matches base name ${baseNameWithExt}: ${isBaseNameMatch}`);
      return isBaseNameMatch;
    });
    
    if (!matchingFile) {
      console.error(`No matching file found for: ${filenameWithoutPrefix}`);
      throw new Error('File not found');
    }
    
    console.log(`Found matching file: ${matchingFile.pathname}`);
    console.log(`Deleting file: ${matchingFile.url}`);
    
    await del(matchingFile.url);
    console.log(`File deleted successfully: ${matchingFile.url}`);
    
    return { success: true };
  } catch (error) {
    console.error('Error in deleteUserFile:', error);
    throw error;
  }
}
