'use client'

import { useState } from 'react'

type FallbackPDFViewerProps = {
  fileUrl: string
  fileName: string
}

export default function FallbackPDFViewer({ fileUrl, fileName }: FallbackPDFViewerProps) {
  const [isLoading, setIsLoading] = useState(true)

  const handleIframeLoad = () => {
    setIsLoading(false)
  }

  return (
    <div className="h-full relative bg-gray-100 dark:bg-gray-900">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-700 z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading document...</p>
          </div>
        </div>
      )}
      
      <iframe
        src={fileUrl}
        className="w-full h-full border-0"
        title={`PDF Viewer - ${fileName}`}
        onLoad={handleIframeLoad}
      />
      
      {/* Overlay message about text selection */}
      <div className="absolute top-4 left-4 right-4 bg-amber-100 dark:bg-amber-900/20 border border-amber-300 dark:border-amber-700 rounded-lg p-3 shadow-lg">
        <div className="flex items-start gap-2">
          <svg className="w-5 h-5 text-amber-600 dark:text-amber-400 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <div>
            <p className="text-sm text-amber-800 dark:text-amber-200 font-medium">
              Basic PDF Viewer Mode
            </p>
            <p className="text-xs text-amber-700 dark:text-amber-300 mt-1">
              Direct text selection is not available. Use the "Add Text from PDF" button to manually add text for Q&A pairs.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
