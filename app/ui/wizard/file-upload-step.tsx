'use client'

import { useState } from 'react'
import FileBrowser from '../file-manager/file-browser'
import FileUpload from '../file-manager/file-upload'
import { SelectedFile } from '../document-wizard'

type FileUploadStepProps = {
  onFileSelected: (file: SelectedFile) => void
}

export default function FileUploadStep({ onFileSelected }: FileUploadStepProps) {
  const [selectedFileId, setSelectedFileId] = useState<string | null>(null)
  const [files, setFiles] = useState<SelectedFile[]>([])
  const [refreshKey, setRefreshKey] = useState(0)

  const handleFileSelection = (fileId: string, fileList: SelectedFile[]) => {
    setSelectedFileId(fileId)
    setFiles(fileList)
  }

  const handleUploadComplete = () => {
    // Trigger a refresh of the file list
    setRefreshKey(prev => prev + 1)
  }

  const handleProceedToEdit = () => {
    if (selectedFileId) {
      const selectedFile = files.find(file => file.id === selectedFileId)
      if (selectedFile) {
        onFileSelected(selectedFile)
      }
    }
  }

  const selectedFile = selectedFileId ? files.find(file => file.id === selectedFileId) : null

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Step 1: Upload or Select Document
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Upload a new PDF document or select an existing one to proceed to editing.
        </p>
      </div>

      {/* Upload Section */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Upload New Document
        </h3>
        <div className="max-w-md">
          <FileUpload onUploadComplete={handleUploadComplete} />
        </div>
      </div>

      {/* File Browser Section */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Or Select Existing Document
        </h3>
        <FileBrowser 
          key={refreshKey}
          selectionMode="single"
          onFileSelection={handleFileSelection}
          showUpload={false}
        />
      </div>

      {/* Selected File Info and Proceed Button */}
      {selectedFile && (
        <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex-shrink-0">
                <svg className="w-8 h-8 text-red-500" fill="currentColor" viewBox="0 0 384 512">
                  <path d="M181.9 256.1c-5.1-9.9-9.1-21.4-9.1-33.4 0-1.1.1-2.2.1-3.4-11.2 5.2-22.2 10.8-32.1 17.1-11.5 7.2-22.5 14.8-33.1 22.1-1.3.9-2.7 1.7-4 2.6 4.8 9.5 9.5 18.9 15.2 27.2 1.5 2.2 3.1 4.3 4.7 6.4 5.4-3.7 10.8-7.3 16.4-10.6 15.9-9.5 33-17 50.9-21.1-3-2.3-5.8-4.7-8.4-7.2l-.6-.6z M0 64C0 28.7 28.7 0 64 0H224V128c0 17.7 14.3 32 32 32H384V448c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V64zm384 64H256V0L384 128z"/>
                </svg>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                  {selectedFile.name}
                </h4>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB • Uploaded {new Date(selectedFile.uploadedAt).toLocaleDateString()}
                </p>
              </div>
            </div>
            
            <button
              onClick={handleProceedToEdit}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Proceed to Edit
              <svg className="ml-2 -mr-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
