'use client'

import { useState } from 'react'
import { SelectedFile } from '../document-wizard'
import dynamic from 'next/dynamic'
import QAComponent from './qa-component'
import ClientPDF<PERSON>iewer from './client-pdf-viewer'

// Dynamically import the interactive PDF viewer to prevent SSR issues
const InteractivePDFViewer = dynamic(() => import('./interactive-pdf-viewer'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-full bg-gray-100 dark:bg-gray-900">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600 dark:text-gray-400">Loading advanced PDF viewer...</p>
      </div>
    </div>
  )
})

export type HighlightedPhrase = {
  id: string
  text: string
  question: string
  answer: string
  position?: {
    x: number
    y: number
    width: number
    height: number
  }
}

type DocumentEditStepProps = {
  file: SelectedFile
  onBack: () => void
}

export default function DocumentEditStep({ file, onBack }: DocumentEditStepProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [highlightedPhrases, setHighlightedPhrases] = useState<HighlightedPhrase[]>([])
  const [selectedText, setSelectedText] = useState<string>('')
  const [isHighlightMode, setIsHighlightMode] = useState(false)
  const [manualText, setManualText] = useState<string>('')
  const [useInteractivePDF, setUseInteractivePDF] = useState(false)
  const [pdfViewerError, setPdfViewerError] = useState<string | null>(null)
  const [hasTriedInteractive, setHasTriedInteractive] = useState(false)

  const handleIframeLoad = () => {
    setIsLoading(false)
  }

  const handleTextSelection = () => {
    if (!isHighlightMode) return

    const selection = window.getSelection()
    if (selection && selection.toString().trim()) {
      const selectedText = selection.toString().trim()
      console.log('Text selected:', selectedText) // Debug log
      addHighlightedPhrase(selectedText)
      selection.removeAllRanges()
    }
  }

  // Handle direct text selection from interactive PDF viewer
  const handlePDFTextSelected = (selectedText: string) => {
    console.log('PDF text selected directly:', selectedText)
    addHighlightedPhrase(selectedText)
  }

  // Alternative method for capturing text from PDF
  const handleManualTextAdd = (text: string) => {
    if (text.trim()) {
      addHighlightedPhrase(text.trim())
    }
  }

  const handleInteractiveViewerToggle = () => {
    if (!useInteractivePDF) {
      setHasTriedInteractive(true)
    }
    setUseInteractivePDF(!useInteractivePDF)
  }

  const addHighlightedPhrase = (text: string) => {
    const newPhrase: HighlightedPhrase = {
      id: Date.now().toString(),
      text,
      question: `What does "${text}" refer to?`,
      answer: ''
    }

    setHighlightedPhrases(prev => [...prev, newPhrase])
  }

  const updatePhraseAnswer = (id: string, answer: string) => {
    setHighlightedPhrases(prev =>
      prev.map(phrase =>
        phrase.id === id ? { ...phrase, answer } : phrase
      )
    )
  }

  const updatePhraseQuestion = (id: string, question: string) => {
    setHighlightedPhrases(prev =>
      prev.map(phrase =>
        phrase.id === id ? { ...phrase, question } : phrase
      )
    )
  }

  const updatePhraseText = (id: string, text: string) => {
    setHighlightedPhrases(prev =>
      prev.map(phrase =>
        phrase.id === id ? { ...phrase, text } : phrase
      )
    )
  }

  const removePhrase = (id: string) => {
    setHighlightedPhrases(prev => prev.filter(phrase => phrase.id !== id))
  }

  const addSamplePhrases = () => {
    const samplePhrases: HighlightedPhrase[] = [
      {
        id: 'sample-1',
        text: 'Policy Number: INS-2024-001',
        question: 'What is the policy number?',
        answer: 'INS-2024-001'
      },
      {
        id: 'sample-2',
        text: 'Coverage Amount: $500,000',
        question: 'What is the coverage amount?',
        answer: '$500,000'
      },
      {
        id: 'sample-3',
        text: 'Effective Date: January 1, 2024',
        question: 'When does the policy become effective?',
        answer: 'January 1, 2024'
      }
    ]

    setHighlightedPhrases(prev => [...prev, ...samplePhrases])
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="border-b border-gray-200 dark:border-gray-700 p-4 bg-white dark:bg-gray-800">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-1">
              Step 2: Edit Document
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {useInteractivePDF
                ? 'Select text directly from the PDF to create Q&A pairs'
                : 'Copy and paste text from the PDF to create Q&A pairs'
              }: <span className="font-medium">{file.name}</span>
            </p>
          </div>

          <div className="flex items-center space-x-3">
            {highlightedPhrases.length === 0 && (
              <button
                onClick={addSamplePhrases}
                className="inline-flex items-center px-3 py-2 border border-blue-300 dark:border-blue-600 text-sm font-medium rounded-md text-blue-700 dark:text-blue-300 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/40"
              >
                <svg className="mr-2 -ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                Try Demo
              </button>
            )}

            <button
              onClick={() => setIsHighlightMode(!isHighlightMode)}
              className={`inline-flex items-center px-3 py-2 border text-sm font-medium rounded-md transition-colors ${
                isHighlightMode
                  ? 'border-yellow-500 bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                  : 'border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
              }`}
            >
              <svg className="mr-2 -ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              {isHighlightMode ? 'Exit Text Mode' : 'Add Text from PDF'}
            </button>

            <button
              onClick={handleInteractiveViewerToggle}
              className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
              title={useInteractivePDF ? 'Switch to standard PDF viewer' : 'Try advanced PDF viewer with direct text selection'}
            >
              <svg className="mr-2 -ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
              </svg>
              {useInteractivePDF ? 'Standard View' : 'Try Advanced View'}
            </button>

            <button
              onClick={onBack}
              className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
            >
              <svg className="mr-2 -ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
              </svg>
              Back
            </button>

            <button className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700">
              <svg className="mr-2 -ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
              </svg>
              Export Q&A
            </button>
          </div>
        </div>

        {isHighlightMode && (
          <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-md">
            <p className="text-sm text-blue-800 dark:text-blue-200">
              {useInteractivePDF
                ? '📝 Selection mode is active. Click and drag to select text directly from the PDF to create Q&A pairs.'
                : '📝 Text input mode is active. Copy text from the PDF and paste it in the input field to create Q&A pairs.'
              }
            </p>
          </div>
        )}
      </div>

      {/* Split View */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Side - PDF Viewer */}
        <div className="flex-1 relative">
          {useInteractivePDF ? (
            <InteractivePDFViewer
              fileUrl={file.url}
              onTextSelected={handlePDFTextSelected}
              isSelectionMode={isHighlightMode}
            />
          ) : (
            <ClientPDFViewer
              fileUrl={file.url}
              fileName={file.name}
              onTextSelected={handlePDFTextSelected}
              isSelectionMode={isHighlightMode}
            />
          )}
        </div>

        {/* Right Side - Q&A Panel */}
        <div className="w-96 border-l border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 flex flex-col">
          {/* Q&A Header */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Questions & Answers
              </h3>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                {highlightedPhrases.length} Q&A{highlightedPhrases.length !== 1 ? 's' : ''}
              </span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Each highlighted phrase creates a Q&A pair below
            </p>
          </div>

          {/* Q&A List */}
          <div className="flex-1 overflow-y-auto p-4">
            {highlightedPhrases.length === 0 ? (
              <div className="text-center py-8">
                <svg className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Q&A pairs yet</h4>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                  Click "Add Text from PDF" and copy-paste text from the PDF to create your first Q&A pair.
                </p>

                {/* Manual text input for testing */}
                <div className="mt-6 p-4 bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                  <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                    Or manually add text for testing:
                  </h5>
                  <div className="flex gap-2">
                    <input
                      type="text"
                      placeholder="Enter text to highlight..."
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          const input = e.target as HTMLInputElement
                          if (input.value.trim()) {
                            addHighlightedPhrase(input.value.trim())
                            input.value = ''
                          }
                        }
                      }}
                    />
                    <button
                      onClick={(e) => {
                        const input = (e.target as HTMLButtonElement).previousElementSibling as HTMLInputElement
                        if (input.value.trim()) {
                          addHighlightedPhrase(input.value.trim())
                          input.value = ''
                        }
                      }}
                      className="px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                    >
                      Add
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {highlightedPhrases.map((phrase, index) => (
                  <QAComponent
                    key={phrase.id}
                    phrase={phrase}
                    index={index}
                    onUpdateQuestion={updatePhraseQuestion}
                    onUpdateAnswer={updatePhraseAnswer}
                    onUpdateText={updatePhraseText}
                    onRemove={removePhrase}
                  />
                ))}

                {/* Add more button */}
                <div className="pt-4 border-t border-gray-200 dark:border-gray-600">
                  <div className="flex gap-2">
                    <input
                      type="text"
                      placeholder="Add more highlighted text..."
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          const input = e.target as HTMLInputElement
                          if (input.value.trim()) {
                            addHighlightedPhrase(input.value.trim())
                            input.value = ''
                          }
                        }
                      }}
                    />
                    <button
                      onClick={(e) => {
                        const input = (e.target as HTMLButtonElement).previousElementSibling as HTMLInputElement
                        if (input.value.trim()) {
                          addHighlightedPhrase(input.value.trim())
                          input.value = ''
                        }
                      }}
                      className="px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                    >
                      Add
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
