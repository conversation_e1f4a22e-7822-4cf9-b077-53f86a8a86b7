'use client'

import { useState } from 'react'
import { HighlightedPhrase } from './document-edit-step'

type QAComponentProps = {
  phrase: HighlightedPhrase
  index: number
  onUpdateQuestion: (id: string, question: string) => void
  onUpdateAnswer: (id: string, answer: string) => void
  onUpdateText: (id: string, text: string) => void
  onRemove: (id: string) => void
}

export default function QAComponent({
  phrase,
  index,
  onUpdateQuestion,
  onUpdateAnswer,
  onUpdateText,
  onRemove
}: QAComponentProps) {
  const [isEditingQuestion, setIsEditingQuestion] = useState(false)
  const [tempQuestion, setTempQuestion] = useState(phrase.question)

  const handleQuestionSave = () => {
    onUpdateQuestion(phrase.id, tempQuestion)
    setIsEditingQuestion(false)
  }

  const handleQuestionCancel = () => {
    setTempQuestion(phrase.question)
    setIsEditingQuestion(false)
  }

  return (
    <div className="bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 p-4 mb-4">
      {/* Header with highlighted text and remove button */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
              #{index + 1}
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400">Highlighted Text</span>
          </div>
          <div className="mb-3">
            <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
              Highlighted Text (editable)
            </label>
            <textarea
              value={phrase.text}
              onChange={(e) => onUpdateText(phrase.id, e.target.value)}
              className="w-full px-3 py-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded text-sm text-gray-900 dark:text-white font-medium resize-none focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
              rows={2}
              placeholder="Enter the text you highlighted from the PDF..."
            />
          </div>
        </div>
        <button
          onClick={() => onRemove(phrase.id)}
          className="ml-2 p-1 text-gray-400 hover:text-red-500 transition-colors"
          title="Remove this Q&A"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* Question Section */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Question
          </label>
          {!isEditingQuestion && (
            <button
              onClick={() => setIsEditingQuestion(true)}
              className="text-xs text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
            >
              Edit
            </button>
          )}
        </div>

        {isEditingQuestion ? (
          <div className="space-y-2">
            <textarea
              value={tempQuestion}
              onChange={(e) => setTempQuestion(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white resize-none"
              rows={2}
              placeholder="Enter your question..."
            />
            <div className="flex gap-2">
              <button
                onClick={handleQuestionSave}
                className="px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700"
              >
                Save
              </button>
              <button
                onClick={handleQuestionCancel}
                className="px-3 py-1 bg-gray-300 text-gray-700 text-xs rounded hover:bg-gray-400"
              >
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded px-3 py-2">
            <p className="text-sm text-gray-900 dark:text-white">
              {phrase.question}
            </p>
          </div>
        )}
      </div>

      {/* Answer Section */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Answer
        </label>
        <textarea
          value={phrase.answer}
          onChange={(e) => onUpdateAnswer(phrase.id, e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          rows={3}
          placeholder="Enter your answer here..."
        />
        <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
          {phrase.answer.length} characters
        </div>
      </div>
    </div>
  )
}
