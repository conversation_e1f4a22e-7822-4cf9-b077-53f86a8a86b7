'use client'

import { useState, useEffect, useRef } from 'react'

type ClientPDFViewerProps = {
  fileUrl: string
  fileName: string
  onTextSelected: (selectedText: string) => void
  isSelectionMode: boolean
}

export default function ClientPDFViewer({ 
  fileUrl, 
  fileName, 
  onTextSelected, 
  isSelectionMode 
}: ClientPDFViewerProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [manualText, setManualText] = useState('')
  const [isClient, setIsClient] = useState(false)
  const iframeRef = useRef<HTMLIFrameElement>(null)

  // Ensure we're on the client side
  useEffect(() => {
    setIsClient(true)
  }, [])

  const handleIframeLoad = () => {
    setIsLoading(false)
  }

  const handleManualTextAdd = () => {
    if (manualText.trim()) {
      onTextSelected(manualText.trim())
      setManualText('')
    }
  }

  // Try to capture text selection from iframe (limited by CORS)
  useEffect(() => {
    if (!isSelectionMode || !isClient) return

    const handleMessage = (event: MessageEvent) => {
      // Listen for messages from PDF viewer if it supports it
      if (event.data && event.data.type === 'textSelected') {
        onTextSelected(event.data.text)
      }
    }

    window.addEventListener('message', handleMessage)
    return () => window.removeEventListener('message', handleMessage)
  }, [isSelectionMode, isClient, onTextSelected])

  if (!isClient) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-100 dark:bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading PDF viewer...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-gray-100 dark:bg-gray-900">
      {/* PDF Controls */}
      <div className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <svg className="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 384 512">
            <path d="M181.9 256.1c-5.1-9.9-9.1-21.4-9.1-33.4 0-1.1.1-2.2.1-3.4-11.2 5.2-22.2 10.8-32.1 17.1-11.5 7.2-22.5 14.8-33.1 22.1-1.3.9-2.7 1.7-4 2.6 4.8 9.5 9.5 18.9 15.2 27.2 1.5 2.2 3.1 4.3 4.7 6.4 5.4-3.7 10.8-7.3 16.4-10.6 15.9-9.5 33-17 50.9-21.1-3-2.3-5.8-4.7-8.4-7.2l-.6-.6z M0 64C0 28.7 28.7 0 64 0H224V128c0 17.7 14.3 32 32 32H384V448c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V64zm384 64H256V0L384 128z"/>
          </svg>
          <span className="text-sm text-gray-700 dark:text-gray-300 font-medium">
            {fileName}
          </span>
        </div>

        {isSelectionMode && (
          <div className="flex items-center space-x-2">
            <div className="flex items-center px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-xs">
              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
              </svg>
              Text Input Mode
            </div>
          </div>
        )}
      </div>

      {/* PDF Document */}
      <div className="flex-1 relative">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-700 z-10">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600 dark:text-gray-400">Loading document...</p>
            </div>
          </div>
        )}
        
        <iframe
          ref={iframeRef}
          src={fileUrl}
          className="w-full h-full border-0"
          title={`PDF Viewer - ${fileName}`}
          onLoad={handleIframeLoad}
        />

        {/* Text input overlay when selection mode is active */}
        {isSelectionMode && (
          <div className="absolute top-4 left-4 right-4 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg p-4 shadow-lg z-20">
            <div className="flex items-center gap-3">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Copy text from the PDF and paste it here:
                </label>
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={manualText}
                    onChange={(e) => setManualText(e.target.value)}
                    placeholder="Paste or type text from the PDF here..."
                    className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter' && manualText.trim()) {
                        handleManualTextAdd()
                      }
                    }}
                    autoFocus
                  />
                  <button
                    onClick={handleManualTextAdd}
                    disabled={!manualText.trim()}
                    className="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Add Q&A
                  </button>
                </div>
              </div>
            </div>
            <div className="mt-3 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded">
              <p className="text-xs text-blue-800 dark:text-blue-200">
                💡 <strong>How to use:</strong> Select text in the PDF above, copy it (Ctrl+C or Cmd+C), then paste it in the input field to create Q&A pairs
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
