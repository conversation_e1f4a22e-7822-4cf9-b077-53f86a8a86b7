'use client'

import { useState } from 'react'
import FileUploadStep from './wizard/file-upload-step'
import DocumentEditStep from './wizard/document-edit-step'

export type SelectedFile = {
  id: string
  name: string
  url: string
  size: number
  uploadedAt: string
}

export type WizardStep = 'upload' | 'edit'

export default function DocumentWizard() {
  const [currentStep, setCurrentStep] = useState<WizardStep>('upload')
  const [selectedFile, setSelectedFile] = useState<SelectedFile | null>(null)

  const handleFileSelected = (file: SelectedFile) => {
    setSelectedFile(file)
    setCurrentStep('edit')
  }

  const handleBackToUpload = () => {
    setCurrentStep('upload')
    setSelectedFile(null)
  }

  const renderStepIndicator = () => (
    <div className="mb-8">
      <div className="flex items-center justify-center space-x-4">
        {/* Step 1 */}
        <div className="flex items-center">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
            currentStep === 'upload' 
              ? 'bg-blue-600 text-white' 
              : selectedFile 
                ? 'bg-green-600 text-white' 
                : 'bg-gray-300 text-gray-600'
          }`}>
            {selectedFile ? '✓' : '1'}
          </div>
          <span className={`ml-2 text-sm font-medium ${
            currentStep === 'upload' ? 'text-blue-600' : 'text-gray-500'
          }`}>
            Upload Document
          </span>
        </div>

        {/* Connector */}
        <div className={`w-16 h-0.5 ${selectedFile ? 'bg-green-600' : 'bg-gray-300'}`} />

        {/* Step 2 */}
        <div className="flex items-center">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
            currentStep === 'edit' 
              ? 'bg-blue-600 text-white' 
              : 'bg-gray-300 text-gray-600'
          }`}>
            2
          </div>
          <span className={`ml-2 text-sm font-medium ${
            currentStep === 'edit' ? 'text-blue-600' : 'text-gray-500'
          }`}>
            Edit Document
          </span>
        </div>
      </div>
    </div>
  )

  return (
    <div className="max-w-6xl mx-auto">
      {renderStepIndicator()}
      
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        {currentStep === 'upload' && (
          <FileUploadStep onFileSelected={handleFileSelected} />
        )}
        
        {currentStep === 'edit' && selectedFile && (
          <DocumentEditStep 
            file={selectedFile} 
            onBack={handleBackToUpload}
          />
        )}
      </div>
    </div>
  )
}
