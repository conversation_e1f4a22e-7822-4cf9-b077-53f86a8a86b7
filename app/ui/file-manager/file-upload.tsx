'use client'

import { useState, useRef, useCallback } from 'react'
import { validateFile } from '@/app/lib/file-validation'

type FileUploadProps = {
  onUploadComplete: () => void
}

export default function FileUpload({ onUploadComplete }: FileUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [uploadError, setUploadError] = useState<string | null>(null)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [isDragging, setIsDragging] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Handle drag events
  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(false)
  }, [])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(false)

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0]

      // Validate file
      const validation = validateFile(file)
      if (!validation.isValid) {
        setUploadError(validation.error || 'Invalid file')
        setSelectedFile(null)
        return
      }

      setSelectedFile(file)
      setUploadError(null)
    }
  }, [])

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0]

      // Validate file
      const validation = validateFile(file)
      if (!validation.isValid) {
        setUploadError(validation.error || 'Invalid file')
        setSelectedFile(null)
        return
      }

      setSelectedFile(file)
      setUploadError(null)
    }
  }

  // Handle file upload
  async function handleUpload(event: React.FormEvent) {
    event.preventDefault()

    if (!selectedFile) {
      setUploadError('Please select a file')
      return
    }

    try {
      setUploading(true)
      setUploadError(null)
      setUploadProgress(0)

      // Create form data for the upload
      const formData = new FormData()
      formData.append('file', selectedFile)

      // Upload to Vercel Blob using the /api/upload endpoint with progress tracking
      const xhr = new XMLHttpRequest()

      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100)
          setUploadProgress(progress)
        }
      })

      xhr.addEventListener('load', async () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText)
            console.log('Upload response:', response)

            // Verify the upload was successful
            if (response.success) {
              console.log('File uploaded successfully:', response.url)

              // Optional: Verify the file exists in blob storage
              const verifyResponse = await fetch(`/api/verify-upload?filename=${encodeURIComponent(selectedFile.name)}`)
              const verifyResult = await verifyResponse.json()

              if (verifyResult.success) {
                console.log('Upload verified in blob storage:', verifyResult.file)
              } else {
                console.warn('Upload verification failed:', verifyResult.error)
              }
            }

            // Reset the file input and selected file
            if (fileInputRef.current) {
              fileInputRef.current.value = ''
            }
            setSelectedFile(null)
            setUploadProgress(100)

            // Notify parent component that upload is complete
            onUploadComplete()
          } catch (parseError) {
            console.error('Error parsing upload response:', parseError)
            setUploadError('Upload completed but response parsing failed')
          }
        } else {
          try {
            const errorResponse = JSON.parse(xhr.responseText)
            setUploadError(errorResponse.error || 'Failed to upload file')
          } catch {
            setUploadError('Failed to upload file')
          }
        }
        setUploading(false)
      })

      xhr.addEventListener('error', () => {
        setUploadError('Failed to upload file')
        setUploading(false)
      })

      xhr.open('POST', '/api/upload')
      xhr.send(formData)

    } catch (error) {
      console.error('Upload error:', error)
      setUploadError('Failed to upload file')
      setUploading(false)
    }
  }

  return (
    <div className="w-full max-w-md">
      <form onSubmit={handleUpload} className="space-y-4">
        <div
          className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
            isDragging
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
              : 'border-gray-300 dark:border-gray-700 hover:border-blue-400 dark:hover:border-blue-600'
          }`}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        >
          <div className="flex flex-col items-center justify-center space-y-3">
            <svg
              className="w-12 h-12 text-gray-400 dark:text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
              />
            </svg>

            <div className="text-sm text-gray-600 dark:text-gray-400">
              <label className="relative cursor-pointer rounded-md font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 focus-within:outline-none">
                <span>Upload a file</span>
                <input
                  type="file"
                  ref={fileInputRef}
                  className="sr-only"
                  accept=".pdf,application/pdf"
                  onChange={handleFileChange}
                  disabled={uploading}
                />
              </label>
              <p className="pl-1">or drag and drop</p>
            </div>

            <p className="text-xs text-gray-500 dark:text-gray-400">
              PDF files only, up to 10MB
            </p>
          </div>
        </div>

        {selectedFile && (
          <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
            <div className="flex items-center space-x-2">
              <svg
                className="w-6 h-6 text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              <span className="text-sm truncate max-w-[200px]">{selectedFile.name}</span>
            </div>
            <button
              type="button"
              onClick={() => setSelectedFile(null)}
              className="text-red-500 hover:text-red-700"
              disabled={uploading}
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        )}

        {uploading && (
          <div className="space-y-2">
            <div className="flex justify-between text-xs text-gray-600 dark:text-gray-400">
              <span>Uploading...</span>
              <span>{uploadProgress}%</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${uploadProgress}%` }}
              />
            </div>
          </div>
        )}

        {uploadError && (
          <p className="text-sm text-red-600 dark:text-red-500">{uploadError}</p>
        )}

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={uploading || !selectedFile}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {uploading ? 'Uploading...' : 'Upload'}
          </button>
        </div>
      </form>
    </div>
  )
}
