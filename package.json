{"name": "insure", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@vercel/blob": "^0.27.3", "next": "15.2.4", "next-auth": "^5.0.0-beta.25", "pdfjs-dist": "^5.3.31", "react": "^19.0.0", "react-dom": "^19.0.0", "react-pdf": "^10.0.1", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "copy-webpack-plugin": "^13.0.0", "eslint": "^9", "eslint-config-next": "15.2.4", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "^5"}}