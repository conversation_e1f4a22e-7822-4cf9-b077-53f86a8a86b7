import NextAuth from 'next-auth'
import Google from 'next-auth/providers/google'

// Add these debug logs at the top of your file
console.log('Auth module initialization');
console.log('Environment check:', {
  NEXTAUTH_URL: process.env.NEXTAUTH_URL,
  NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET ? 'Set' : 'Not set',
  GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID ? 'Set' : 'Not set',
  GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET ? 'Set' : 'Not set'
});

export const { handlers, auth, signIn, signOut } = NextAuth({
  providers: [
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code"
        }
      }
    })
  ],
  session: { strategy: 'jwt' },
  pages: {
    signIn: '/login',
    error: '/auth-error'
  },
  callbacks: {
    async signIn({ user, account, profile }) {
      console.log('Sign-in callback triggered', { 
        user: user ? { id: user.id, name: user.name, email: user.email } : 'No user',
        account: account ? { provider: account.provider, type: account.type } : 'No account',
        profile: profile ? 'Profile exists' : 'No profile'
      });
      return true;
    },
    async redirect({ url, baseUrl }) {
      console.log('Redirect callback triggered with:', { url, baseUrl });
      
      // Always redirect to dashboard after successful login
      if (url.startsWith(baseUrl) && url.includes('/api/auth')) {
        return `${baseUrl}/dashboard`;
      }
      
      // Otherwise return the URL as is
      return url;
    },
    async session({ session, token }) {
      // Make sure user ID from token is added to the session
      if (session.user && token.sub) {
        session.user.id = token.sub;
      }
      
      console.log('Session callback:', { 
        session: session ? 'Session exists' : 'No session',
        userId: session?.user?.id,
        tokenSub: token?.sub
      });
      
      return session;
    },
    async jwt({ token, account, profile }) {
      console.log('JWT callback:', { token });
      return token;
    }
  },
  debug: true,
  logger: {
    error(code, ...message) {
      console.error('AUTH ERROR:', code, ...message);
    },
    warn(code, ...message) {
      console.warn('AUTH WARNING:', code, ...message);
    },
    debug(code, ...message) {
      console.log('AUTH DEBUG:', code, ...message);
    }
  }
})

console.log('NextAuth handlers available:', Object.keys(handlers));
console.log('NextAuth initialized');
console.log('Expected Google callback URL:', `${process.env.NEXTAUTH_URL}/api/auth/callback/google`);
